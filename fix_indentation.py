#!/usr/bin/env python3
"""
修复mdp_gui.py中的缩进错误
"""

def fix_indentation():
    file_path = "gui_source/mdp_gui.py"
    
    # 读取文件
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 找到有问题的行并修复
    fixed_lines = []
    skip_mode = False

    for i, line in enumerate(lines):
        line_num = i + 1

        # 检查是否是有问题的区域开始（缩进错误的代码）
        if '"""强制格式化为HH:MM:SS时间显示' in line and line.startswith('                '):
            skip_mode = True
            print(f"找到问题区域开始：第 {line_num} 行")
            continue

        # 检查是否是有问题的区域结束
        if skip_mode and 'old_bottom_axis = plot_item.getAxis(\'bottom\')' in line and not line.startswith('                '):
            skip_mode = False
            print(f"找到问题区域结束：第 {line_num} 行")
            # 这行是正确的，直接添加
            fixed_lines.append(line)
            continue

        # 如果在跳过模式中，跳过这些行
        if skip_mode:
            print(f"跳过第 {line_num} 行: {line.strip()[:50]}...")
            continue

        # 其他行保持不变
        fixed_lines.append(line)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print(f"修复完成！处理了 {len(lines)} 行，输出了 {len(fixed_lines)} 行")

if __name__ == "__main__":
    fix_indentation()
