# -*- mode: python ; coding: utf-8 -*-

block_cipher = None


a = Analysis(
    ["mdp_main.py", "mdp_gui.py"],
    pathex=["..", "."],  # for github action
    binaries=[],
    datas=[
        ("./icon.ico", "."),
        ("./mdp_gui_template/mainwindow_en.qm", "."),  # 主要翻译文件
        ("./mdp_gui_template/updates_en.qm", "."),     # updates翻译文件
        ("./en_US.qm", "."),                          # 备用翻译文件
        ("./PingFang Bold.ttf", "."),                 # 统一使用PingFang字体
        ("./Li-ion.csv", "."),
    ],
    hiddenimports=["pyi_splash"],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        "llvm",
        "llvmlite", 
        "numba",
        "matplotlib",
        "scipy",
        "pandas",
        "jupyter",
        "notebook", 
        "IPython",
        "tornado",
        "zmq",
        "cv2",
        "sklearn",
        "tensorflow",
        "torch",
        "PIL.ImageTk",
        "tkinter",
        "turtle",
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)
splash = Splash(
    "booting.png",
    binaries=a.binaries,
    datas=a.datas,
    text_pos=(166, 473),
    text_size=12,
    text_color="#898989",
    max_img_size=(760, 480),
    always_on_top=False,
)
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    splash,  # <-- both, splash target
    splash.binaries,  # <-- and splash binaries
    [],
    name="WPX-GUI",
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon="icon.ico",
)

# 注释掉 COLLECT 部分以生成单个 exe 文件（one-file 模式）
# coll = COLLECT(
#     exe,
#     a.binaries,
#     a.zipfiles,
#     a.datas,
#     strip=True,
#     upx=True,
#     upx_exclude=[],
#     name="WPX-GUI",
# )
